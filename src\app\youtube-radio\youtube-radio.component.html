<div class="content-container w-full h-full flex flex-col gap-4 p-4 bg-base-200" [class.has-player]="currentVideoId">

  <!-- 主題切換選單 -->
  <div class="w-full flex justify-end">
    <app-theme-switcher></app-theme-switcher>
  </div>

  <!-- 播放清單快速訪問區域 -->
  <div class="playlist-quick-access-section">
    <div class="card bg-base-100 shadow-xl rounded-2xl border border-base-300 w-full max-w-4xl mx-auto">
      <div class="card-body p-6">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-bold text-base-content">我的播放清單</h3>
        </div>

        <!-- 播放清單快速訪問網格 -->
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          <!-- 播放清單卡片 -->
          <div *ngFor="let playlist of getDisplayPlaylists()"
               class="playlist-quick-card bg-base-200 rounded-xl overflow-hidden hover:bg-base-300 transition-all duration-200 cursor-pointer group"
               (click)="goToPlaylist(playlist.id)">

            <!-- 縮圖區域 -->
            <div class="playlist-thumbnails-container">
              <div class="thumbnails-grid" *ngIf="getPlaylistThumbnails(playlist.id).length > 0; else noThumbnails">
                <!-- 顯示最多4張縮圖 -->
                <div *ngFor="let thumbnail of getPlaylistThumbnails(playlist.id).slice(0, 4); let i = index"
                     class="thumbnail-item">
                  <img [src]="thumbnail"
                       [alt]="'歌曲縮圖 ' + (i + 1)"
                       class="w-full h-full object-cover"
                       loading="lazy">
                </div>
              </div>

              <!-- 沒有縮圖時的預設圖示 -->
              <ng-template #noThumbnails>
                <div class="default-thumbnail">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-primary/60" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
                  </svg>
                </div>
              </ng-template>
            </div>

            <!-- 播放清單資訊 -->
            <div class="p-3">
              <div class="text-sm font-medium line-clamp-2 leading-tight text-center">{{ playlist.name }}</div>
              <div class="text-xs text-base-content/60 text-center mt-1">
                {{ getPlaylistItemCount(playlist.id) }} 首歌曲
              </div>
            </div>
          </div>

          <!-- 管理播放清單按鈕 -->
          <div class="playlist-quick-card bg-base-200 rounded-xl overflow-hidden hover:bg-primary/10 transition-all duration-200 cursor-pointer group border-2 border-dashed border-base-300 hover:border-primary/50"
               (click)="goToPlaylistManager()">

            <!-- 管理按鈕內容 -->
            <div class="h-full flex flex-col items-center justify-center p-6">
              <!-- 管理圖示 -->
              <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-3 group-hover:bg-primary/20 transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              </div>
              <!-- 管理文字 -->
              <div class="text-sm font-medium text-primary text-center">管理播放清單</div>
            </div>
          </div>
        </div>

        <!-- 空狀態 -->
        <div *ngIf="availablePlaylists.length === 0" class="text-center py-8">
          <div class="mb-6">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto text-base-content/30" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
            </svg>
          </div>
          <p class="text-base-content/70 mb-6">還沒有播放清單</p>
          <button class="btn btn-primary btn-sm" (click)="goToPlaylistManager()">
            建立第一個播放清單
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 輸入區域 -->
  <div class="input-section">
    <div class="card bg-base-100 shadow-xl rounded-2xl border border-base-300 w-full">
      <div class="card-body p-4">
        <textarea
          rows="5"
          class="textarea textarea-bordered w-full bg-base-100 text-base-content rounded-xl"
          [(ngModel)]="urlInput"
          placeholder="請輸入 YouTube 網址（每行一個）"
        ></textarea>
        <div class="mt-4 flex gap-2 justify-end">
          <button class="btn btn-primary btn-sm rounded shadow" (click)="loadPlaylist()">加入播放清單</button>
          <button class="btn btn-secondary btn-sm rounded shadow" (click)="clearPlaylist()">清除播放清單</button>
        </div>
      </div>
    </div>
  </div>

  <!-- 播放清單區域 -->
  <div class="playlist-container">
    <div class="card bg-base-100 shadow-xl rounded-2xl border border-base-300 w-full">
      <div class="card-body p-4">
        <div class="flex justify-between items-center mb-2">
          <h3 class="text-lg font-bold text-base-content">播放清單</h3>
          <button class="btn btn-ghost btn-sm rounded-full" (click)="shufflePlaylist()" [disabled]="playlist.length <= 1" title="隨機排序">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" /></svg>
          </button>
        </div>
        <div class="divide-y divide-base-300" cdkDropList (cdkDropListDropped)="onDrop($event)">
          @for (video of playlist; track $index; let i = $index) {
            <div class="py-2 flex justify-between items-center text-base-content transition rounded-xl"
                [class.bg-primary]="i === currentIndex"
                [class.text-primary-content]="i === currentIndex"
                cdkDrag
                [cdkDragDisabled]="i === currentIndex">
              <div class="flex items-center gap-2 flex-1 min-w-0">
                <span class="cursor-move flex-shrink-0" cdkDragHandle>
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-base-content opacity-60" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" /></svg>
                </span>
                <div class="flex-1 min-w-0">
                  <div class="truncate font-medium">{{ video.title || video.id }}</div>
                </div>
              </div>
              <div class="flex gap-2 flex-shrink-0">
                <button class="btn btn-ghost btn-xs rounded-full" (click)="playIndex(i)" [disabled]="i === currentIndex" title="播放">
                  <svg class="size-[1.2em]" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g stroke-linejoin="round" stroke-linecap="round" stroke-width="2" fill="none" stroke="currentColor"><path d="M6 3L20 12 6 21 6 3z"></path></g></svg>
                </button>
                <button class="btn btn-ghost btn-xs rounded-full" (click)="openAddToPlaylistModal(video)" title="加入播放清單">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-success" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                  </svg>
                </button>
                <button class="btn btn-ghost btn-xs rounded-full" (click)="removeFromPlaylist(i)" title="移除">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-error" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg>
                </button>
              </div>
            </div>
          }
        </div>
      </div>
    </div>
  </div>
</div>

<!-- 單一 YouTube 播放器 - 通過 CSS 控制位置 -->
<div class="youtube-player-container"
     [class.mini-mode]="!isPlayerExpanded"
     [class.expanded-mode]="isPlayerExpanded">

  @if(currentVideoId || playlist.length > 0) {
    <!-- 有影片時顯示 YouTube 播放器 -->
    <youtube-player
      [videoId]="currentVideoId || playlist[0].id"
      [width]="videoWidth || 640"
      [height]="videoHeight || 360"
      [playerVars]="playerConfig"
      (stateChange)="onPlayerStateChange($event)"
      (ready)="onPlayerReady($event)"
      #youtubePlayer
    ></youtube-player>
  } @else {
    <!-- 沒有播放清單時的佔位符 -->
    <div class="player-placeholder"
         [style.width.px]="videoWidth || 640"
         [style.height.px]="videoHeight || 360">
      <div class="placeholder-content">
        <i class="bi bi-youtube" style="font-size: 4rem; color: #ccc;"></i>
        <p class="text-muted mt-3">請添加影片到播放清單</p>
      </div>
    </div>
  }
</div>

<!-- 底部播放條 -->
@if(playlist.length > 0) {
  <div class="player-bar" [class.expanded]="isPlayerExpanded">
    <!-- 迷你播放器 (收起狀態) -->
    @if(!isPlayerExpanded) {
      <div class="mini-player" (click)="togglePlayer()">
        <div class="mini-player-info">
          <div class="mini-player-thumbnail">
            <!-- 縮圖位置會被播放器覆蓋 -->
          </div>
          <div class="mini-player-details">
            <div class="mini-player-title">{{ getCurrentVideoTitle() }}</div>
          </div>
        </div>
        <div class="mini-player-controls" (click)="$event.stopPropagation()">
          <button class="btn-control" (click)="playPrevious()" [disabled]="currentIndex <= 0" title="上一首">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <button class="btn-control btn-play" (click)="togglePlayPause()" title="播放/暫停">
            @if(isPlaying) {
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6" />
              </svg>
            } @else {
              <svg class="size-[1.2em]" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g stroke-linejoin="round" stroke-linecap="round" stroke-width="2" fill="none" stroke="currentColor"><path d="M6 3L20 12 6 21 6 3z"></path></g></svg>
            }
          </button>
          <button class="btn-control" (click)="playNext()" [disabled]="currentIndex >= playlist.length - 1" title="下一首">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
          </button>
          <button class="btn-control" (click)="togglePlayer()" title="展開播放器">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 14l5-5 5 5" />
            </svg>
          </button>
        </div>
      </div>
    }

    <!-- 展開的播放器 -->
    @if(isPlayerExpanded) {
      <div class="expanded-player">
        <div class="expanded-player-header">
          <button class="btn-control" (click)="togglePlayer()" title="收起播放器">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7-7-7" />
            </svg>
          </button>
          <div class="expanded-player-title">正在播放</div>
          <div></div>
        </div>

        <div class="expanded-player-content">
          <!-- 左側：YouTube 播放器和控制 -->
          <div class="expanded-player-left">
            <!-- YouTube 播放器顯示區域 -->
            <div class="video-player-target" id="video-target">
              <!-- 播放器在展開模式下會通過 CSS 定位到這裡 -->
              @if(isPlayerExpanded) {
                <div class="video-info-overlay visible">
                  <div class="video-title">{{ getCurrentVideoTitle() }}</div>
                </div>
              }
            </div>

            <!-- 播放控制 -->
            <div class="expanded-player-controls">
              <button class="btn-control-lg" (click)="playPrevious()" [disabled]="currentIndex <= 0" title="上一首">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              <button class="btn-control-lg btn-play-lg" (click)="togglePlayPause()" title="播放/暫停">
                @if(isPlaying) {
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6" />
                  </svg>
                } @else {
                  <svg class="size-[1.2em]" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g stroke-linejoin="round" stroke-linecap="round" stroke-width="2" fill="none" stroke="currentColor"><path d="M6 3L20 12 6 21 6 3z"></path></g></svg>
                }
              </button>
              <button class="btn-control-lg" (click)="playNext()" [disabled]="currentIndex >= playlist.length - 1" title="下一首">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </div>
          </div>

          <!-- 右側：播放清單 -->
          <div class="expanded-player-right">
            <div class="playlist-container h-full">
              <div class="card bg-base-100 shadow-xl rounded-2xl border border-base-300 w-full h-full">
                <div class="card-body p-4 flex flex-col h-full">
                  <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-bold text-base-content">播放清單</h3>
                    <button class="btn btn-ghost btn-sm rounded-full" (click)="shufflePlaylist()" [disabled]="playlist.length <= 1" title="隨機排序">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" /></svg>
                    </button>
                  </div>
                  <div class="flex-1 overflow-y-auto divide-y divide-base-300" cdkDropList (cdkDropListDropped)="onDrop($event)">
                    @for (video of playlist; track video.id; let i = $index) {
                      <div class="py-3 flex justify-between items-center text-base-content transition rounded-xl hover:bg-base-200"
                          [class.bg-primary]="i === currentIndex"
                          [class.text-primary-content]="i === currentIndex"
                          [class.hover:bg-primary-focus]="i === currentIndex"
                          cdkDrag
                          [cdkDragDisabled]="i === currentIndex">
                        <div class="flex items-center gap-3 flex-1 min-w-0">
                          <span class="cursor-move flex-shrink-0" cdkDragHandle>
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-base-content opacity-60" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" /></svg>
                          </span>
                          <div class="flex-1 min-w-0">
                            <div class="truncate font-medium">{{ video.title || video.id }}</div>
                          </div>
                        </div>
                        <div class="flex gap-1 flex-shrink-0">
                          <button class="btn btn-ghost btn-xs rounded-full" (click)="playIndex(i)" [disabled]="i === currentIndex" title="播放">
                            <svg class="size-[1.2em]" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g stroke-linejoin="round" stroke-linecap="round" stroke-width="2" fill="none" stroke="currentColor"><path d="M6 3L20 12 6 21 6 3z"></path></g></svg>
                          </button>
                          <button class="btn btn-ghost btn-xs rounded-full" (click)="openAddToPlaylistModal(video)" title="加入播放清單">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-success" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                            </svg>
                          </button>
                          <button class="btn btn-ghost btn-xs rounded-full" (click)="removeFromPlaylist(i)" title="移除">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-error" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg>
                          </button>
                        </div>
                      </div>
                    }
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    }
  </div>
}

<!-- 加入播放清單模態框 -->
<div class="modal" [class.modal-open]="showAddToPlaylistModal">
  <div class="modal-box">
    <h3 class="font-bold text-lg mb-4">加入播放清單</h3>

    <div *ngIf="selectedVideoForPlaylist" class="mb-4 p-3 bg-base-200 rounded-lg">
      <div class="text-sm text-base-content/70">選擇的歌曲：</div>
      <div class="font-medium">{{ selectedVideoForPlaylist.title }}</div>
    </div>

    <div class="mb-6">
      <div class="text-sm font-medium mb-3">選擇播放清單：</div>

      <div *ngIf="availablePlaylists.length === 0" class="text-center py-8">
        <div class="text-base-content/50 mb-4">還沒有播放清單</div>
        <button class="btn btn-primary btn-sm" (click)="goToPlaylistManager()">
          建立播放清單
        </button>
      </div>

      <div *ngIf="availablePlaylists.length > 0" class="space-y-2 max-h-60 overflow-y-auto">
        <div *ngFor="let playlist of availablePlaylists"
             class="flex items-center justify-between p-3 bg-base-200 rounded-lg hover:bg-base-300 transition-colors cursor-pointer"
             (click)="addToPlaylist(playlist.id)">
          <div>
            <div class="font-medium">{{ playlist.name }}</div>
            <div *ngIf="playlist.description" class="text-sm text-base-content/70">{{ playlist.description }}</div>
          </div>
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
          </svg>
        </div>
      </div>
    </div>

    <div class="modal-action">
      <button class="btn btn-ghost" (click)="closeAddToPlaylistModal()">取消</button>
      <button class="btn btn-primary" (click)="goToPlaylistManager()">
        管理播放清單
      </button>
    </div>
  </div>
  <div class="modal-backdrop" (click)="closeAddToPlaylistModal()"></div>
</div>

<!-- Toast 通知 -->
<div class="toast toast-top toast-end" *ngIf="showToast">
  <div class="alert"
       [class.alert-success]="toastType === 'success'"
       [class.alert-error]="toastType === 'error'"
       [class.alert-info]="toastType === 'info'">
    <svg *ngIf="toastType === 'success'" xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
    <svg *ngIf="toastType === 'error'" xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
    <svg *ngIf="toastType === 'info'" xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
    <span>{{ toastMessage }}</span>
    <button class="btn btn-sm btn-ghost" (click)="showToast = false">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
      </svg>
    </button>
  </div>
</div>