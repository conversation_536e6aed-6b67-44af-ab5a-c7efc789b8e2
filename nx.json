{"$schema": "./node_modules/nx/schemas/nx-schema.json", "targetDefaults": {"build": {"cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production"]}, "test": {"cache": true, "inputs": ["default", "^production"]}}, "namedInputs": {"sharedGlobals": [], "default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/**/*.spec.[jt]s"]}, "nxCloudId": "68401b98e075b76d4d95f934"}