  ::ng-deep .p-card {
    width: 100% !important;
  }

  ::ng-deep youtube-player {
    width: 100% !important;
  }

:host {
  display: block;
  height: 100%;
  overflow-y: auto;
  background: hsl(var(--b2));
  color: hsl(var(--bc));
}

// YouTube 播放器容器 - 根據模式調整大小和位置
.youtube-player-container {
  position: relative; // 為覆蓋層提供定位基準
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &.mini-mode {
    // 迷你模式：定位到底部播放條的縮圖位置
    position: fixed !important;
    bottom: 12px !important;
    left: calc(25% + 16px) !important; // sidebar 寬度 + padding
    width: 48px !important;
    height: 48px !important;
    top: auto !important; // 覆蓋 JavaScript 設定的 top
    z-index: 1001 !important;
    border-radius: 6px !important;
    overflow: hidden !important;
    box-shadow: 0 2px 8px hsl(var(--bc) / 0.15) !important;
    background: transparent !important; // 迷你模式不需要黑色背景

    // 手機版時調整位置
    @media (max-width: 768px) {
      left: 16px !important;
    }
  }

  &.expanded-mode {
    // 展開模式：位置由 JavaScript 動態設定
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 32px hsl(var(--bc) / 0.2);
    background: #000;

    @media (max-width: 768px) {
      border-radius: 8px;
    }
  }

  // 播放器佔位符樣式
  .player-placeholder {
    background: #000;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;

    .placeholder-content {
      text-align: center;
      color: #ccc;
    }
  }

  // 播放器覆蓋層樣式（當播放清單為空時顯示）
  .player-overlay {
    position: absolute;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;

    .placeholder-content {
      text-align: center;
      color: #ccc;
    }
  }

  youtube-player {
    width: 100% !important;
    height: 100% !important;

    iframe {
      position: absolute;
      top: 0;
      left: 0;
      width: 100% !important;
      height: 100% !important;
      border-radius: inherit;
    }
  }
}

.content-container {
  width: 100%;
  height: 100vh;
  padding: 1rem;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: stretch; // 改為 stretch，讓子元素可以自由控制對齊
  overflow-y: auto;
  overflow-x: hidden;

  // 自定義滾動條樣式
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: hsl(var(--b2));
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: hsl(var(--bc) / 0.3);
    border-radius: 3px;

    &:hover {
      background: hsl(var(--bc) / 0.5);
    }
  }

  // Firefox 滾動條樣式
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--bc) / 0.3) hsl(var(--b2));

  &.has-player {
    padding-bottom: 80px; // 為底部播放器留出空間
  }

  // 當聊天室開啟時，為大螢幕添加右側間距
  @media (min-width: 1024px) {
    &.chat-open {
      padding-right: 320px; // 300px (聊天室寬度) + 20px (間距)
      align-items: flex-end; // 靠右對齊

      // 添加內部容器來控制實際內容的位置
      > * {
        width: calc(100% - 160px); // 留出一些空間，避免內容太靠右
        margin-right: 40px; // 微調右側間距
      }
    }
  }

  @media (max-width: 640px) {
    height: auto;
    min-height: 100vh;
  }
}

.main-content {
  width: 100%;
  max-width: 1000px;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  flex: 1;
  align-self: center; // 讓主要內容置中
}

.playlist-section {
  width: 100%;
}

.input-section {
  width: 100%;
  flex-shrink: 0;
}

// 底部播放控制欄
.player-bar {
  position: fixed;
  bottom: 0;
  left: 25%; // 避開左側 sidebar (25% 寬度)
  width: 75%; // 只佔右側區域
  background: hsl(var(--b1) / 0.95);
  backdrop-filter: blur(20px);
  box-shadow: 0 -2px 20px hsl(var(--bc) / 0.1);
  z-index: 999;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-top: 1px solid hsl(var(--bc) / 0.1);

  &.expanded {
    height: 100vh;
    background: hsl(var(--b1) / 0.98);
    overflow-y: auto;
  }

  // 手機版時佔滿全寬
  @media (max-width: 768px) {
    left: 0;
    width: 100%;
  }
}

// 迷你播放器 (收起狀態)
.mini-player {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  height: 72px;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background: hsl(var(--bc) / 0.05);
  }

  &-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
    min-width: 0;
  }

  &-thumbnail {
    width: 48px;
    height: 48px;
    border-radius: 6px;
    overflow: hidden;
    flex-shrink: 0;
    box-shadow: 0 2px 8px hsl(var(--bc) / 0.15);

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  &-details {
    flex: 1;
    min-width: 0;
  }

  &-title {
    font-weight: 500;
    font-size: 14px;
    color: hsl(var(--bc));
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.4;
  }

  &-controls {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-shrink: 0;
  }
}

// 展開的播放器
.expanded-player {
  display: flex;
  flex-direction: column;
  height: 100vh;

  &-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid hsl(var(--bc) / 0.1);
    background: hsl(var(--b1) / 0.8);
    backdrop-filter: blur(10px);
  }

  &-title {
    font-weight: 600;
    font-size: 18px;
    color: hsl(var(--bc));
    text-align: center;
    flex: 1;
  }

  &-content {
    flex: 1;
    display: flex;
    flex-direction: row;
    padding: 20px;
    overflow-y: auto;
    overflow-x: hidden; // 防止橫向卷軸
    gap: 20px;
    box-sizing: border-box; // 確保 padding 包含在總寬度內

    // 手機版時改為垂直排列
    @media (max-width: 768px) {
      flex-direction: column;
    }
  }

  &-left {
    display: flex;
    flex-direction: column;
    gap: 16px;
    min-width: 0; // 防止 flex 項目溢出
    flex: 2; // 影片區域佔 2/3 的空間

    // 手機版時佔滿寬度
    @media (max-width: 768px) {
      flex: 1;
    }
  }

  &-right {
    display: flex;
    flex-direction: column;
    min-width: 0; // 防止 flex 項目溢出
    flex: 1; // 播放清單佔 1/3 的空間

    // 手機版時佔滿寬度
    @media (max-width: 768px) {
      flex: 1;
    }
  }

  &-controls {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 16px;
  }
}

// 影片播放器目標區域
.video-player-target {
  width: 100%;
  aspect-ratio: 16/9;
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  // 預設透明背景
  background: transparent;

  @media (max-width: 768px) {
    border-radius: 8px;
  }

  // 當有播放器時顯示背景和陰影
  &.has-player {
    background: #000;
    box-shadow: 0 8px 32px hsl(var(--bc) / 0.2);
  }
}

// 移除重複的樣式，已整合到上面

.video-info-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, hsl(var(--bc) / 0.8));
  padding: 20px;
  color: white;
  pointer-events: none; // 避免干擾播放器控制
  opacity: 0;
  transition: opacity 0.3s ease;

  &.visible {
    opacity: 1;
  }
}

.video-title {
  font-size: 18px;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  line-height: 1.4;
}

// 保留原有的縮圖樣式（用於其他地方）
.video-thumbnail {
  width: 100%;
  height: 100%;
  position: relative;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

// 舊的縮圖容器樣式 (保留用於其他地方)
.video-info-container {
  width: 100%;
  aspect-ratio: 16/9;
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 32px hsl(var(--bc) / 0.2);

  @media (max-width: 768px) {
    border-radius: 8px;
  }
}

// 控制按鈕樣式
.btn-control {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  cursor: pointer;
  color: hsl(var(--bc) / 0.6);
  transition: all 0.2s ease;

  &:hover {
    background: hsl(var(--bc) / 0.1);
    color: hsl(var(--bc) / 0.8);
    transform: scale(1.05);
  }

  &:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    transform: none;
  }
}

.btn-play {
  background: hsl(var(--p));
  color: hsl(var(--pc));

  &:hover {
    background: hsl(var(--p) / 0.8);
    color: hsl(var(--pc));
  }
}

.btn-control-lg {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  cursor: pointer;
  color: hsl(var(--bc) / 0.6);
  transition: all 0.2s ease;

  &:hover {
    background: hsl(var(--bc) / 0.1);
    color: hsl(var(--bc) / 0.8);
    transform: scale(1.05);
  }

  &:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    transform: none;
  }
}

.btn-play-lg {
  width: 72px;
  height: 72px;
  background: hsl(var(--p));
  color: hsl(var(--pc));
  box-shadow: 0 4px 16px hsl(var(--p) / 0.3);

  &:hover {
    background: hsl(var(--p) / 0.8);
    color: hsl(var(--pc));
    box-shadow: 0 6px 20px hsl(var(--p) / 0.4);
  }
}



// 播放清單快速訪問樣式
.playlist-quick-access-section {
  .playlist-quick-card {
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px hsl(var(--bc) / 0.1);
    }

    .line-clamp-2 {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      word-break: break-word;
      text-align: center;
    }
  }

  // 播放清單縮圖容器
  .playlist-thumbnails-container {
    width: 100%;
    aspect-ratio: 16/9;
    position: relative;
    background: hsl(var(--b3));
    border-radius: 8px 8px 0 0;
    overflow: hidden;
  }

  // 縮圖網格佈局
  .thumbnails-grid {
    width: 100%;
    height: 100%;
    display: grid;
    gap: 1px;

    // 根據子元素數量動態調整佈局
    &:has(.thumbnail-item:nth-child(1):last-child) {
      // 1張圖片：佔滿整個區域
      grid-template-columns: 1fr;
      grid-template-rows: 1fr;
    }

    &:has(.thumbnail-item:nth-child(2):last-child) {
      // 2張圖片：左右分割
      grid-template-columns: 1fr 1fr;
      grid-template-rows: 1fr;
    }

    &:has(.thumbnail-item:nth-child(3):last-child) {
      // 3張圖片：左邊一張大圖，右邊兩張小圖
      grid-template-columns: 2fr 1fr;
      grid-template-rows: 1fr 1fr;

      .thumbnail-item:nth-child(1) {
        grid-row: 1 / 3;
        grid-column: 1;
      }
      .thumbnail-item:nth-child(2) {
        grid-row: 1;
        grid-column: 2;
      }
      .thumbnail-item:nth-child(3) {
        grid-row: 2;
        grid-column: 2;
      }
    }

    &:has(.thumbnail-item:nth-child(4):last-child) {
      // 4張圖片：2x2 網格
      grid-template-columns: 1fr 1fr;
      grid-template-rows: 1fr 1fr;
    }
  }

  .thumbnail-item {
    overflow: hidden;
    background: hsl(var(--b3));

    img {
      transition: transform 0.2s ease;
    }

    &:hover img {
      transform: scale(1.05);
    }
  }

  // 預設縮圖樣式
  .default-thumbnail {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: hsl(var(--b3));
  }
}

// 播放清單樣式
.playlist-container {
  width: 100%;

  .divide-y > div {
    padding: 0.5rem 0;
  }
}

// 拖曳相關樣式
.cdk-drag {
  transition: box-shadow 200ms cubic-bezier(0, 0, 0.2, 1);
}

.pi-bars {
  cursor: move;
  
  &:active {
    cursor: grabbing;
  }
}

.cdk-drag-preview {
  box-shadow: 0 5px 5px -3px hsl(var(--bc) / 0.2),
              0 8px 10px 1px hsl(var(--bc) / 0.14),
              0 3px 14px 2px hsl(var(--bc) / 0.12);
  background-color: hsl(var(--b1));
  border-radius: 4px;
  padding: 0.5rem;
}

.cdk-drag-placeholder {
  opacity: 0.3;
  background: hsl(var(--bc) / 0.1);
  border: 1px dashed hsl(var(--p));
  border-radius: 4px;
}

.cdk-drag-animating {
  transition: none;
}

.divide-y > div {
  &.cdk-drag-animating {
    transition: none;
  }
}

// 確保 YouTube 播放器正確顯示
::ng-deep {
  .youtube-player-container {
    youtube-player {
      width: 100% !important;
      height: 100% !important;

      iframe {
        position: absolute;
        top: 0;
        left: 0;
        width: 100% !important;
        height: 100% !important;
        border-radius: inherit;
      }
    }
  }
}
